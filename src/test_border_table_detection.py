#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试border=1的table检测
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from 国家能源局表头去除table import preview_border_tables, remove_border_tables

def main():
    """测试border=1的table检测和删除"""
    input_file = "/Users/<USER>/Downloads/国家能源局_整合_20250813_220931.xlsx"
    
    if os.path.exists(input_file):
        print("=" * 60)
        print("测试border=1的table检测和删除")
        print("=" * 60)
        
        try:
            # 预览数据
            preview_border_tables(input_file)
            
            print(f"\n开始自动处理...")
            # 自动处理文件
            output_file = remove_border_tables(input_file)
            print(f"\n✅ 处理成功！输出文件: {output_file}")
            
        except Exception as e:
            print(f"\n❌ 处理失败: {e}")
    else:
        print(f"文件不存在: {input_file}")

if __name__ == "__main__":
    main()
