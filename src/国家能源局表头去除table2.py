#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
国家能源局表头去除table程序
功能：
1. 读取国家能源局整合Excel文件
2. 筛选html列中包含border=1的table标签的行
3. 删除这些行并保存到新文件
"""

import pandas as pd
import re
import os
from pathlib import Path
from datetime import datetime


def has_border_table(html_content):
    """
    检查HTML内容是否包含border=1的table标签

    Args:
        html_content (str): HTML内容

    Returns:
        bool: 如果包含border=1的table标签返回True，否则返回False
    """
    if pd.isna(html_content) or not isinstance(html_content, str):
        return False

    # 检查是否包含border=1的table标签（不区分大小写）
    # 匹配各种可能的格式：border="1", border='1', border=1
    pattern = r'<table[^>]*border\s*=\s*["\']?1["\']?[^>]*>'
    return bool(re.search(pattern, html_content, re.IGNORECASE))


def preview_border_tables(input_file_path, num_samples=5):
    """
    预览包含border=1的table的数据

    Args:
        input_file_path (str): 输入文件路径
        num_samples (int): 显示样例数量
    """
    try:
        print(f"正在预览文件: {input_file_path}")
        df = pd.read_excel(input_file_path)

        print(f"文件总行数: {len(df)}")
        print(f"列名: {list(df.columns)}")

        if 'html' not in df.columns:
            print("错误: 文件中没有找到html列")
            return

        # 统计包含border=1的table的行数
        has_border_count = df['html'].apply(has_border_table).sum()

        print(f"\n📊 统计结果:")
        print(f"- 包含border=1的table的行数: {has_border_count}")
        print(f"- 删除后剩余行数: {len(df) - has_border_count}")
        print(f"- 删除比例: {has_border_count/len(df)*100:.2f}%")

        # 显示包含border=1的table的样例
        if has_border_count > 0:
            print(f"\n--- 包含border=1的table的样例 ---")
            sample_count = 0
            for idx, row in df.iterrows():
                html_content = row['html']
                if pd.notna(html_content) and isinstance(html_content, str):
                    if has_border_table(html_content) and sample_count < num_samples:
                        sample_count += 1
                        title = row.get('标题', f'行{idx+1}')
                        category = row.get('分类', '未知')

                        print(f"\n样例 {sample_count}:")
                        print(f"标题: {title}")
                        print(f"分类: {category}")

                        # 查找border=1的table标签
                        pattern = r'<table[^>]*border\s*=\s*["\']?1["\']?[^>]*>'
                        matches = list(re.finditer(pattern, html_content, re.IGNORECASE))
                        if matches:
                            for i, match in enumerate(matches[:2]):  # 最多显示2个匹配
                                table_tag = match.group()
                                print(f"  Table标签 {i+1}: {table_tag}")

                        # 显示HTML内容片段
                        preview = html_content[:200] + "..." if len(html_content) > 200 else html_content
                        print(f"  HTML片段: {preview}")
                        print("-" * 50)
        else:
            print("未找到包含border=1的table的行")

    except Exception as e:
        print(f"预览文件时出错: {e}")


def remove_border_tables(input_file_path, output_file_path=None):
    """
    删除包含border=1的table的行并保存到新文件

    Args:
        input_file_path (str): 输入文件路径
        output_file_path (str, optional): 输出文件路径，如果不指定则自动生成

    Returns:
        str: 输出文件路径
    """
    try:
        print(f"正在处理文件: {input_file_path}")

        # 检查输入文件是否存在
        if not os.path.exists(input_file_path):
            raise FileNotFoundError(f"输入文件不存在: {input_file_path}")

        # 读取Excel文件
        df = pd.read_excel(input_file_path)

        print(f"文件读取成功，共 {len(df)} 行数据")

        if 'html' not in df.columns:
            raise ValueError("文件中没有找到html列")

        # 记录原始行数
        original_row_count = len(df)

        # 创建布尔掩码，标记需要删除的行
        rows_to_delete = df['html'].apply(has_border_table)
        deleted_count = rows_to_delete.sum()

        print(f"\n处理统计:")
        print(f"- 原始行数: {original_row_count}")
        print(f"- 包含border=1的table的行数: {deleted_count}")
        print(f"- 删除比例: {deleted_count/original_row_count*100:.2f}%")

        # 删除包含border=1的table的行
        df_cleaned = df[~rows_to_delete].copy()

        print(f"- 删除后剩余行数: {len(df_cleaned)}")

        # 生成输出文件路径
        if output_file_path is None:
            input_path = Path(input_file_path)
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file_path = input_path.parent / f"{input_path.stem}_去除border表格_{timestamp}.xlsx"

        # 确保输出目录存在
        output_path = Path(output_file_path)
        output_path.parent.mkdir(parents=True, exist_ok=True)

        # 保存处理后的数据
        print(f"\n正在保存到: {output_file_path}")
        with pd.ExcelWriter(output_file_path, engine='openpyxl') as writer:
            df_cleaned.to_excel(writer, index=False, sheet_name='清理后数据')

            # 设置列宽
            worksheet = writer.sheets['清理后数据']
            for column in worksheet.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 50)
                worksheet.column_dimensions[column_letter].width = adjusted_width

        print(f"\n✅ 处理完成！")
        print(f"- 输入文件: {input_file_path}")
        print(f"- 输出文件: {output_file_path}")
        print(f"- 原始行数: {original_row_count}")
        print(f"- 删除行数: {deleted_count}")
        print(f"- 最终行数: {len(df_cleaned)}")

        # 显示分类统计
        if '分类' in df_cleaned.columns:
            print(f"\n📊 删除后的分类统计:")
            category_counts = df_cleaned['分类'].value_counts()
            for category, count in category_counts.items():
                print(f"  {category}: {count} 行")

        return str(output_file_path)

    except Exception as e:
        print(f"处理文件时发生错误: {e}")
        raise


def main():
    """主函数"""
    input_file = "/Users/<USER>/Downloads/国家能源局_整合_20250813_220931.xlsx"

    try:
        # 检查文件是否存在
        if not os.path.exists(input_file):
            print(f"错误: 输入文件不存在 - {input_file}")
            return

        print("=" * 60)
        print("国家能源局表头去除table程序")
        print("=" * 60)

        # 先预览数据
        preview_border_tables(input_file)

        # 询问是否继续处理
        print(f"\n⚠️  将删除所有包含border=1的table标签的行")
        response = input("是否继续处理？(y/n): ").lower().strip()

        if response in ['y', 'yes', '是', 'Y']:
            # 处理文件
            output_file = remove_border_tables(input_file)
            print(f"\n🎉 处理成功！输出文件: {output_file}")
        else:
            print("已取消处理")

    except Exception as e:
        print(f"\n❌ 程序执行失败: {e}")


if __name__ == "__main__":
    main()