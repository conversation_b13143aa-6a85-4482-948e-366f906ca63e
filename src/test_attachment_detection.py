#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试附件检测
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from 国家能源局表头去除table2附件 import preview_attachment_data, add_attachment_field

def main():
    """测试附件检测和字段添加"""
    input_file = "/Users/<USER>/Downloads/国家能源局_整合_20250813_220931_去除border表格_20250813_221625_去除MsoNormalTable_20250813_222141.xlsx"
    
    if os.path.exists(input_file):
        print("=" * 70)
        print("测试附件检测和字段添加")
        print("=" * 70)
        
        try:
            # 预览数据
            preview_attachment_data(input_file)
            
            print(f"\n开始自动处理...")
            # 自动处理文件
            output_file = add_attachment_field(input_file)
            print(f"\n✅ 处理成功！输出文件: {output_file}")
            
        except Exception as e:
            print(f"\n❌ 处理失败: {e}")
    else:
        print(f"文件不存在: {input_file}")

if __name__ == "__main__":
    main()
