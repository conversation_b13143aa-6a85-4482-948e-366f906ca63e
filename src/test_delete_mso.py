#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试删除MsoNormalTable行
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from 国家能源局 import process_excel_file

def main():
    """测试删除MsoNormalTable模式"""
    input_file = "/Users/<USER>/Downloads/国家能源局/政策通知/国家能源局-公告.xlsx"
    
    if os.path.exists(input_file):
        print("测试删除模式：删除包含MsoNormalTable的行")
        try:
            output_file = process_excel_file(input_file, mode="delete_mso")
            print(f"\n✅ 删除MsoNormalTable模式测试成功！输出文件: {output_file}")
        except Exception as e:
            print(f"\n❌ 删除MsoNormalTable模式测试失败: {e}")
    else:
        print(f"文件不存在: {input_file}")

if __name__ == "__main__":
    main()
