#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
国家能源局数据处理脚本
功能：
1. 读取 Excel 文件中的 HTML 字段
2. 删除包含 table 和 td 标签的行
3. 保存到新文件
"""

import pandas as pd
import re
import os
from pathlib import Path


def has_mso_normal_table(html_content):
    """
    检查HTML内容是否包含MsoNormalTable

    Args:
        html_content (str): HTML内容

    Returns:
        bool: 如果包含MsoNormalTable返回True，否则返回False
    """
    if pd.isna(html_content) or not isinstance(html_content, str):
        return False

    # 检查是否包含MsoNormalTable（不区分大小写）
    return bool(re.search(r'MsoNormalTable', html_content, re.IGNORECASE))


def clean_html_content(html_content):
    """
    清理HTML内容，删除包含table和td标签的行

    Args:
        html_content (str): 原始HTML内容

    Returns:
        str: 清理后的HTML内容
    """
    if pd.isna(html_content) or not isinstance(html_content, str):
        return html_content

    # 按行分割HTML内容
    lines = html_content.split('\n')
    cleaned_lines = []

    for line in lines:
        # 检查行中是否包含table或td标签（不区分大小写）
        if not (re.search(r'</?table[^>]*>', line, re.IGNORECASE) or
                re.search(r'</?td[^>]*>', line, re.IGNORECASE)):
            cleaned_lines.append(line)

    return '\n'.join(cleaned_lines)


def process_excel_file(input_file_path, output_file_path=None, mode="delete_mso"):
    """
    处理Excel文件，删除包含MsoNormalTable的行

    Args:
        input_file_path (str): 输入Excel文件路径
        output_file_path (str, optional): 输出文件路径，如果不指定则自动生成
        mode (str): 处理模式
            - "delete_mso": 删除包含MsoNormalTable的行
            - "clean": 清理table标签但保留行数据

    Returns:
        str: 输出文件路径
    """
    try:
        # 检查输入文件是否存在
        if not os.path.exists(input_file_path):
            raise FileNotFoundError(f"输入文件不存在: {input_file_path}")

        print(f"正在读取文件: {input_file_path}")

        # 读取Excel文件
        df = pd.read_excel(input_file_path)

        print(f"文件读取成功，共 {len(df)} 行数据")
        print(f"列名: {list(df.columns)}")

        # 查找HTML相关的列
        html_columns = []
        for col in df.columns:
            if 'html' in col.lower() or '内容' in col or '正文' in col:
                html_columns.append(col)

        if not html_columns:
            print("警告: 未找到HTML相关的列，将处理所有文本列")
            # 如果没有明确的HTML列，处理所有object类型的列
            html_columns = [col for col in df.columns if df[col].dtype == 'object']

        print(f"将处理以下列: {html_columns}")

        # 记录原始行数
        original_row_count = len(df)

        if mode == "delete_mso":
            # 删除包含MsoNormalTable的行
            print(f"使用删除模式：删除包含MsoNormalTable的行")

            # 创建一个布尔掩码，标记需要删除的行
            rows_to_delete = pd.Series([False] * len(df))

            # 检查每个HTML列，如果任何一列包含MsoNormalTable，就标记该行为删除
            for col in html_columns:
                print(f"正在检查列: {col}")
                # 检查每行是否包含MsoNormalTable
                has_mso = df[col].apply(has_mso_normal_table)
                rows_to_delete = rows_to_delete | has_mso

                # 统计该列中包含MsoNormalTable的行数
                mso_count = has_mso.sum()
                print(f"  - 列 '{col}' 中包含MsoNormalTable的行数: {mso_count}")

            # 删除包含MsoNormalTable的行
            df_processed = df[~rows_to_delete].copy()
            deleted_count = rows_to_delete.sum()

        else:
            # 清理模式：清理HTML标签但保留所有行
            print(f"使用清理模式：删除table标签但保留行数据")
            for col in html_columns:
                print(f"正在清理列: {col}")
                df[col] = df[col].apply(clean_html_content)

            df_processed = df.copy()
            deleted_count = 0

        print(f"\n处理统计:")
        print(f"  - 原始行数: {original_row_count}")
        print(f"  - 删除行数: {deleted_count}")
        print(f"  - 剩余行数: {len(df_processed)}")

        # 生成输出文件路径
        if output_file_path is None:
            input_path = Path(input_file_path)
            output_file_path = input_path.parent / f"{input_path.stem}_cleaned{input_path.suffix}"

        # 确保输出目录存在
        output_path = Path(output_file_path)
        output_path.parent.mkdir(parents=True, exist_ok=True)

        # 保存处理后的数据
        print(f"\n正在保存到: {output_file_path}")
        df_processed.to_excel(output_file_path, index=False)

        print(f"\n✅ 处理完成！")
        print(f"- 输入文件: {input_file_path}")
        print(f"- 输出文件: {output_file_path}")
        print(f"- 原始行数: {original_row_count}")
        print(f"- 删除行数: {deleted_count}")
        print(f"- 最终行数: {len(df_processed)}")
        print(f"- 处理列数: {len(html_columns)}")

        return output_file_path

    except Exception as e:
        print(f"处理文件时发生错误: {e}")
        raise


def main():
    """主函数"""
    # 默认文件路径
    default_input_path = "/Users/<USER>/Downloads/国家能源局/政策通知/国家能源局-公告.xlsx"

    # 检查默认路径是否存在
    if os.path.exists(default_input_path):
        input_file = default_input_path
        print(f"使用默认文件路径: {input_file}")
    else:
        print(f"默认文件路径不存在: {default_input_path}")
        print("请确认文件路径或修改脚本中的路径")
        return

    try:
        # 处理文件 - 默认删除包含MsoNormalTable的行
        output_file = process_excel_file(input_file, mode="delete_mso")
        print(f"\n✅ 处理成功！输出文件: {output_file}")

    except Exception as e:
        print(f"\n❌ 处理失败: {e}")


if __name__ == "__main__":
    main()