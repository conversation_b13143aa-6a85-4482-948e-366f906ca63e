#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
国家能源局数据处理脚本
功能：
1. 读取 Excel 文件中的 HTML 字段
2. 删除包含 table 和 td 标签的行
3. 保存到新文件
"""

import pandas as pd
import re
import os
from pathlib import Path


def clean_html_content(html_content):
    """
    清理HTML内容，删除包含table和td标签的行

    Args:
        html_content (str): 原始HTML内容

    Returns:
        str: 清理后的HTML内容
    """
    if pd.isna(html_content) or not isinstance(html_content, str):
        return html_content

    # 按行分割HTML内容
    lines = html_content.split('\n')
    cleaned_lines = []

    for line in lines:
        # 检查行中是否包含table或td标签（不区分大小写）
        if not (re.search(r'</?table[^>]*>', line, re.IGNORECASE) or
                re.search(r'</?td[^>]*>', line, re.IGNORECASE)):
            cleaned_lines.append(line)

    return '\n'.join(cleaned_lines)


def process_excel_file(input_file_path, output_file_path=None):
    """
    处理Excel文件，清理HTML字段中的table和td标签

    Args:
        input_file_path (str): 输入Excel文件路径
        output_file_path (str, optional): 输出文件路径，如果不指定则自动生成

    Returns:
        str: 输出文件路径
    """
    try:
        # 检查输入文件是否存在
        if not os.path.exists(input_file_path):
            raise FileNotFoundError(f"输入文件不存在: {input_file_path}")

        print(f"正在读取文件: {input_file_path}")

        # 读取Excel文件
        df = pd.read_excel(input_file_path)

        print(f"文件读取成功，共 {len(df)} 行数据")
        print(f"列名: {list(df.columns)}")

        # 查找HTML相关的列
        html_columns = []
        for col in df.columns:
            if 'html' in col.lower() or '内容' in col or '正文' in col:
                html_columns.append(col)

        if not html_columns:
            print("警告: 未找到HTML相关的列，将处理所有文本列")
            # 如果没有明确的HTML列，处理所有object类型的列
            html_columns = [col for col in df.columns if df[col].dtype == 'object']

        print(f"将处理以下列: {html_columns}")

        # 处理每个HTML列
        processed_count = 0
        for col in html_columns:
            print(f"正在处理列: {col}")
            original_count = len(df)

            # 清理HTML内容
            df[col] = df[col].apply(clean_html_content)

            # 统计处理的行数
            processed_count += original_count

        # 生成输出文件路径
        if output_file_path is None:
            input_path = Path(input_file_path)
            output_file_path = input_path.parent / f"{input_path.stem}_cleaned{input_path.suffix}"

        # 确保输出目录存在
        output_path = Path(output_file_path)
        output_path.parent.mkdir(parents=True, exist_ok=True)

        # 保存处理后的数据
        print(f"正在保存到: {output_file_path}")
        df.to_excel(output_file_path, index=False)

        print(f"处理完成！")
        print(f"- 输入文件: {input_file_path}")
        print(f"- 输出文件: {output_file_path}")
        print(f"- 处理行数: {len(df)}")
        print(f"- 处理列数: {len(html_columns)}")

        return output_file_path

    except Exception as e:
        print(f"处理文件时发生错误: {e}")
        raise


def main():
    """主函数"""
    # 默认文件路径
    default_input_path = "/Users/<USER>/Downloads/国家能源局/局工作动态/国家能源局-局工作动态.xlsx"

    # 检查默认路径是否存在
    if os.path.exists(default_input_path):
        input_file = default_input_path
        print(f"使用默认文件路径: {input_file}")
    else:
        print(f"默认文件路径不存在: {default_input_path}")
        print("请确认文件路径或修改脚本中的路径")
        return

    try:
        # 处理文件
        output_file = process_excel_file(input_file)
        print(f"\n✅ 处理成功！输出文件: {output_file}")

    except Exception as e:
        print(f"\n❌ 处理失败: {e}")


if __name__ == "__main__":
    main()