#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
国家能源局Excel文件整合脚本
功能：
1. 整合 /Users/<USER>/Downloads/国家能源局 目录下的所有Excel文件
2. 统一表头：标题	正文	发布时间	信息来源	链接	作者	编译	数据库	分类	标签
3. 字段规则：
   - 作者和编译字段为空
   - 数据库固定为"国家能源局"
   - 分类取文件夹名称
   - 标签取文件名字
"""

import pandas as pd
from pathlib import Path
from datetime import datetime


def find_excel_files(base_path):
    """
    查找指定目录下的所有Excel文件

    Args:
        base_path (str): 基础路径

    Returns:
        list: Excel文件路径列表，包含文件夹信息
    """
    excel_files = []
    base_path = Path(base_path)

    if not base_path.exists():
        print(f"路径不存在: {base_path}")
        return excel_files

    # 查找所有Excel文件（.xlsx, .xls）
    for pattern in ['**/*.xlsx', '**/*.xls']:
        for file_path in base_path.glob(pattern):
            if file_path.is_file() and not file_path.name.startswith('~$'):
                # 获取相对于基础路径的文件夹名称
                relative_path = file_path.relative_to(base_path)
                folder_name = relative_path.parent.name if relative_path.parent != Path('.') else '根目录'
                file_name = file_path.stem  # 不包含扩展名的文件名

                excel_files.append({
                    'file_path': str(file_path),
                    'folder_name': folder_name,
                    'file_name': file_name,
                    'relative_path': str(relative_path)
                })

    return excel_files


def read_excel_with_mapping(file_info):
    """
    读取Excel文件并映射到标准字段

    Args:
        file_info (dict): 文件信息字典

    Returns:
        pd.DataFrame: 标准化后的数据框
    """
    try:
        print(f"正在处理: {file_info['relative_path']}")

        # 读取Excel文件
        df = pd.read_excel(file_info['file_path'])

        if df.empty:
            print(f"  - 文件为空，跳过")
            return pd.DataFrame()

        print(f"  - 读取到 {len(df)} 行数据")
        print(f"  - 原始列名: {list(df.columns)}")

        # 创建标准化的数据框
        standardized_data = []

        # 字段映射规则
        field_mapping = {
            '标题': ['标题', 'title', '名称', '主题'],
            '正文': ['正文', '内容', 'content', '文本', 'html'],
            '发布时间': ['发布时间', '时间', 'date', '日期', '发布日期'],
            '信息来源': ['信息来源', '来源', 'source', '出处'],
            '链接': ['链接', 'url', 'link', '网址', '页面网址', '标题链接']
        }

        # 查找每个标准字段对应的原始列
        column_map = {}
        for std_field, possible_names in field_mapping.items():
            for col in df.columns:
                if any(name in str(col).lower() for name in [name.lower() for name in possible_names]):
                    column_map[std_field] = col
                    break

        print(f"  - 字段映射: {column_map}")

        # 处理每一行数据
        for _, row in df.iterrows():
            record = {
                '标题': '',
                '正文': '',
                '发布时间': '',
                '信息来源': '',
                '链接': '',
                '作者': '',  # 固定为空
                '编译': '',  # 固定为空
                '数据库': '国家能源局',  # 固定值
                '分类': file_info['folder_name'],  # 文件夹名称
                '标签': file_info['file_name']  # 文件名
            }

            # 填充映射的字段
            for std_field, orig_col in column_map.items():
                if orig_col in df.columns:
                    value = row[orig_col]
                    if pd.notna(value):
                        record[std_field] = str(value).strip()

            standardized_data.append(record)

        result_df = pd.DataFrame(standardized_data)
        print(f"  - 标准化后: {len(result_df)} 行")

        return result_df

    except Exception as e:
        print(f"  - 处理文件时出错: {e}")
        return pd.DataFrame()


def merge_excel_files(base_path, output_path=None):
    """
    整合所有Excel文件

    Args:
        base_path (str): 基础路径
        output_path (str, optional): 输出文件路径

    Returns:
        str: 输出文件路径
    """
    try:
        print(f"开始整合Excel文件，基础路径: {base_path}")

        # 查找所有Excel文件
        excel_files = find_excel_files(base_path)

        if not excel_files:
            print("未找到任何Excel文件")
            return None

        print(f"找到 {len(excel_files)} 个Excel文件:")
        for file_info in excel_files:
            print(f"  - {file_info['relative_path']} (分类: {file_info['folder_name']}, 标签: {file_info['file_name']})")

        # 定义标准表头
        standard_columns = ['标题', '正文', '发布时间', '信息来源', '链接', '作者', '编译', '数据库', '分类', '标签']

        # 存储所有数据
        all_data = []

        # 处理每个文件
        for file_info in excel_files:
            df = read_excel_with_mapping(file_info)
            if not df.empty:
                all_data.append(df)

        if not all_data:
            print("没有有效的数据可以整合")
            return None

        # 合并所有数据
        print(f"\n正在合并数据...")
        merged_df = pd.concat(all_data, ignore_index=True)

        # 确保列顺序正确
        merged_df = merged_df.reindex(columns=standard_columns)

        print(f"合并完成，总计 {len(merged_df)} 行数据")

        # 生成输出文件路径
        if output_path is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_path = f"/Users/<USER>/Downloads/国家能源局_整合_{timestamp}.xlsx"

        # 确保输出目录存在
        output_path = Path(output_path)
        output_path.parent.mkdir(parents=True, exist_ok=True)

        # 保存到Excel文件
        print(f"正在保存到: {output_path}")
        with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
            merged_df.to_excel(writer, index=False, sheet_name='整合数据')

            # 设置列宽
            worksheet = writer.sheets['整合数据']
            for column in worksheet.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 50)
                worksheet.column_dimensions[column_letter].width = adjusted_width

        print(f"\n✅ 整合完成！")
        print(f"- 处理文件数: {len(excel_files)}")
        print(f"- 总数据行数: {len(merged_df)}")
        print(f"- 输出文件: {output_path}")

        # 显示数据统计
        print(f"\n📊 数据统计:")
        print(f"- 分类统计:")
        category_counts = merged_df['分类'].value_counts()
        for category, count in category_counts.items():
            print(f"  {category}: {count} 行")

        return str(output_path)

    except Exception as e:
        print(f"整合过程中发生错误: {e}")
        raise


def main():
    """主函数"""
    base_path = "/Users/<USER>/Downloads/国家能源局"

    try:
        output_file = merge_excel_files(base_path)
        if output_file:
            print(f"\n🎉 整合成功！输出文件: {output_file}")
        else:
            print("\n❌ 整合失败")
    except Exception as e:
        print(f"\n❌ 程序执行失败: {e}")


if __name__ == "__main__":
    main()
