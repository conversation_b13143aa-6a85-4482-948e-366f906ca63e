#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证MsoNormalTable删除结果
"""

import pandas as pd
import re

def has_mso_normal_table(html_content):
    """检查HTML内容是否包含MsoNormalTable"""
    if pd.isna(html_content) or not isinstance(html_content, str):
        return False
    
    return bool(re.search(r'MsoNormalTable', html_content, re.IGNORECASE))

def verify_mso_deleted():
    """验证MsoNormalTable删除结果"""
    cleaned_file = "/Users/<USER>/Downloads/国家能源局/政策通知/国家能源局-公告_cleaned.xlsx"
    
    try:
        print(f"正在验证删除MsoNormalTable后的文件: {cleaned_file}")
        df = pd.read_excel(cleaned_file)
        
        print(f"文件行数: {len(df)}")
        print(f"列名: {list(df.columns)}")
        
        # 检查HTML列是否还有MsoNormalTable
        html_columns = ['正文', 'html']
        
        total_mso_remaining = 0
        for col in html_columns:
            if col in df.columns:
                # 统计还有MsoNormalTable的行数
                has_mso_count = df[col].apply(has_mso_normal_table).sum()
                print(f"列 '{col}' 中仍包含MsoNormalTable的行数: {has_mso_count}")
                total_mso_remaining += has_mso_count
                
                # 如果还有，显示前几个例子
                if has_mso_count > 0:
                    print(f"⚠️ 列 '{col}' 中仍有MsoNormalTable:")
                    for idx, content in enumerate(df[col]):
                        if pd.notna(content) and isinstance(content, str):
                            if has_mso_normal_table(content):
                                print(f"  行{idx+1}: {content[:100]}...")
                                break
        
        print(f"\n验证结果:")
        print(f"- 处理后总行数: {len(df)}")
        print(f"- 仍包含MsoNormalTable的行数: {total_mso_remaining}")
        
        if total_mso_remaining == 0:
            print("✅ 所有包含MsoNormalTable的行已成功删除")
        else:
            print("⚠️ 仍有部分MsoNormalTable未删除")
            
        # 显示处理后的数据样例
        print(f"\n处理后的数据样例:")
        for i in range(min(3, len(df))):
            title = df['标题'].iloc[i] if '标题' in df.columns else f"行{i+1}"
            print(f"- {title}")
        
    except Exception as e:
        print(f"验证文件时出错: {e}")

if __name__ == "__main__":
    verify_mso_deleted()
