#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
国家能源局表头去除table2附件程序
功能：
1. 读取清理后的Excel文件
2. 检查html列中是否包含a标签，且链接后缀为.pdf、.docx、.doc、.xlsx、.xls
3. 新增"是否附件"字段，标记为"是"或"否"
4. 保存到新文件
"""

import pandas as pd
import re
import os
from pathlib import Path
from datetime import datetime


def has_attachment_links(html_content):
    """
    检查HTML内容是否包含附件链接

    Args:
        html_content (str): HTML内容

    Returns:
        tuple: (是否包含附件, 附件链接列表)
    """
    if pd.isna(html_content) or not isinstance(html_content, str):
        return False, []

    # 定义附件文件扩展名
    attachment_extensions = ['.pdf', '.docx', '.doc', '.xlsx', '.xls']

    # 查找所有a标签的href属性
    # 匹配各种可能的格式：href="...", href='...', href=...
    href_pattern = r'<a[^>]*href\s*=\s*["\']([^"\']+)["\'][^>]*>'
    matches = re.findall(href_pattern, html_content, re.IGNORECASE)

    attachment_links = []
    for link in matches:
        # 检查链接是否以附件扩展名结尾
        for ext in attachment_extensions:
            if link.lower().endswith(ext.lower()):
                attachment_links.append(link)
                break

    return len(attachment_links) > 0, attachment_links


def preview_attachment_data(input_file_path, num_samples=5):
    """
    预览包含附件链接的数据

    Args:
        input_file_path (str): 输入文件路径
        num_samples (int): 显示样例数量
    """
    try:
        print(f"正在预览文件: {input_file_path}")
        df = pd.read_excel(input_file_path)

        print(f"文件总行数: {len(df)}")
        print(f"列名: {list(df.columns)}")

        if 'html' not in df.columns:
            print("错误: 文件中没有找到html列")
            return

        # 统计包含附件链接的行数
        attachment_results = df['html'].apply(has_attachment_links)
        has_attachment_count = sum(result[0] for result in attachment_results)

        print(f"\n📊 统计结果:")
        print(f"- 包含附件链接的行数: {has_attachment_count}")
        print(f"- 不包含附件链接的行数: {len(df) - has_attachment_count}")
        print(f"- 附件比例: {has_attachment_count/len(df)*100:.2f}%")

        # 统计各种附件类型
        attachment_types = {'.pdf': 0, '.docx': 0, '.doc': 0, '.xlsx': 0, '.xls': 0}
        for result in attachment_results:
            if result[0]:  # 如果包含附件
                for link in result[1]:
                    for ext in attachment_types.keys():
                        if link.lower().endswith(ext.lower()):
                            attachment_types[ext] += 1
                            break

        print(f"\n📎 附件类型统计:")
        for ext, count in attachment_types.items():
            if count > 0:
                print(f"  {ext}: {count} 个")

        # 显示包含附件链接的样例
        if has_attachment_count > 0:
            print(f"\n--- 包含附件链接的样例 ---")
            sample_count = 0
            for idx, row in df.iterrows():
                html_content = row['html']
                if pd.notna(html_content) and isinstance(html_content, str):
                    has_attach, attach_links = has_attachment_links(html_content)
                    if has_attach and sample_count < num_samples:
                        sample_count += 1
                        title = row.get('标题', f'行{idx+1}')
                        category = row.get('分类', '未知')

                        print(f"\n样例 {sample_count}:")
                        print(f"标题: {title}")
                        print(f"分类: {category}")
                        print(f"附件数量: {len(attach_links)}")

                        # 显示附件链接
                        for i, link in enumerate(attach_links[:3]):  # 最多显示3个附件
                            # 提取文件名
                            filename = link.split('/')[-1] if '/' in link else link
                            print(f"  附件 {i+1}: {filename}")
                            if len(link) > 100:
                                print(f"    完整链接: {link[:100]}...")
                            else:
                                print(f"    完整链接: {link}")

                        print("-" * 50)
        else:
            print("未找到包含附件链接的行")

    except Exception as e:
        print(f"预览文件时出错: {e}")


def add_attachment_field(input_file_path, output_file_path=None):
    """
    添加"是否附件"字段并保存到新文件

    Args:
        input_file_path (str): 输入文件路径
        output_file_path (str, optional): 输出文件路径，如果不指定则自动生成

    Returns:
        str: 输出文件路径
    """
    try:
        print(f"正在处理文件: {input_file_path}")

        # 检查输入文件是否存在
        if not os.path.exists(input_file_path):
            raise FileNotFoundError(f"输入文件不存在: {input_file_path}")

        # 读取Excel文件
        df = pd.read_excel(input_file_path)

        print(f"文件读取成功，共 {len(df)} 行数据")

        if 'html' not in df.columns:
            raise ValueError("文件中没有找到html列")

        # 记录原始行数
        original_row_count = len(df)

        # 检查每行是否包含附件链接
        print("正在检查附件链接...")
        attachment_results = []
        attachment_count = 0

        for idx, html_content in enumerate(df['html']):
            if idx % 500 == 0:  # 每500行显示一次进度
                print(f"  处理进度: {idx}/{len(df)} ({idx/len(df)*100:.1f}%)")

            has_attach, _ = has_attachment_links(html_content)
            attachment_results.append(has_attach)
            if has_attach:
                attachment_count += 1

        # 添加"是否附件"字段
        df['是否附件'] = ['是' if result else '否' for result in attachment_results]

        print(f"\n处理统计:")
        print(f"- 总行数: {original_row_count}")
        print(f"- 包含附件的行数: {attachment_count}")
        print(f"- 不包含附件的行数: {original_row_count - attachment_count}")
        print(f"- 附件比例: {attachment_count/original_row_count*100:.2f}%")

        # 生成输出文件路径
        if output_file_path is None:
            input_path = Path(input_file_path)
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file_path = input_path.parent / f"{input_path.stem}_添加附件字段_{timestamp}.xlsx"

        # 确保输出目录存在
        output_path = Path(output_file_path)
        output_path.parent.mkdir(parents=True, exist_ok=True)

        # 保存处理后的数据
        print(f"\n正在保存到: {output_file_path}")
        with pd.ExcelWriter(output_file_path, engine='openpyxl') as writer:
            df.to_excel(writer, index=False, sheet_name='添加附件字段')

            # 设置列宽
            worksheet = writer.sheets['添加附件字段']
            for column in worksheet.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 50)
                worksheet.column_dimensions[column_letter].width = adjusted_width

        print(f"\n✅ 处理完成！")
        print(f"- 输入文件: {input_file_path}")
        print(f"- 输出文件: {output_file_path}")
        print(f"- 总行数: {original_row_count}")
        print(f"- 新增字段: 是否附件")

        # 显示分类统计
        if '分类' in df.columns:
            print(f"\n📊 各分类的附件统计:")
            category_attachment = df.groupby('分类')['是否附件'].value_counts().unstack(fill_value=0)
            for category in category_attachment.index:
                yes_count = category_attachment.loc[category, '是'] if '是' in category_attachment.columns else 0
                no_count = category_attachment.loc[category, '否'] if '否' in category_attachment.columns else 0
                total = yes_count + no_count
                percentage = yes_count / total * 100 if total > 0 else 0
                print(f"  {category}: {yes_count}/{total} ({percentage:.1f}%)")

        return str(output_file_path)

    except Exception as e:
        print(f"处理文件时发生错误: {e}")
        raise


def main():
    """主函数"""
    input_file = "/Users/<USER>/Downloads/国家能源局_整合_20250813_220931_去除border表格_20250813_221625_去除MsoNormalTable_20250813_222141.xlsx"

    try:
        # 检查文件是否存在
        if not os.path.exists(input_file):
            print(f"错误: 输入文件不存在 - {input_file}")
            return

        print("=" * 70)
        print("国家能源局表头附件检测程序")
        print("=" * 70)

        # 先预览数据
        preview_attachment_data(input_file)

        # 询问是否继续处理
        print(f"\n⚠️  将添加'是否附件'字段到Excel文件")
        response = input("是否继续处理？(y/n): ").lower().strip()

        if response in ['y', 'yes', '是', 'Y']:
            # 处理文件
            output_file = add_attachment_field(input_file)
            print(f"\n🎉 处理成功！输出文件: {output_file}")
        else:
            print("已取消处理")

    except Exception as e:
        print(f"\n❌ 程序执行失败: {e}")


if __name__ == "__main__":
    main()