#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试MsoNormalTable检测
"""

import pandas as pd
import re
import os


def has_mso_normal_table(html_content):
    """检查HTML内容是否包含MsoNormalTable"""
    if pd.isna(html_content) or not isinstance(html_content, str):
        return False
    
    return bool(re.search(r'MsoNormalTable', html_content, re.IGNORECASE))


def preview_mso_data(input_file_path, num_samples=5):
    """预览包含MsoNormalTable的数据"""
    try:
        print(f"正在检测文件中的MsoNormalTable: {input_file_path}")
        df = pd.read_excel(input_file_path)
        print(f"\n📊 数据概览 (共{len(df)}行):")
        print(f"列名: {list(df.columns)}")
        
        # 查找HTML相关的列
        html_columns = []
        for col in df.columns:
            if 'html' in col.lower() or '内容' in col or '正文' in col:
                html_columns.append(col)
        
        if not html_columns:
            html_columns = [col for col in df.columns if df[col].dtype == 'object']
        
        print(f"\nHTML相关列: {html_columns}")
        
        # 统计包含MsoNormalTable的行数
        total_with_mso = 0
        for col in html_columns:
            has_mso_count = df[col].apply(has_mso_normal_table).sum()
            print(f"列 '{col}' 中包含MsoNormalTable的行数: {has_mso_count}")
            if col == 'html':  # 主要关注html列
                total_with_mso = has_mso_count
        
        print(f"\n如果删除包含MsoNormalTable的行:")
        print(f"- 原始行数: {len(df)}")
        print(f"- 将删除: {total_with_mso} 行")
        print(f"- 剩余行数: {len(df) - total_with_mso}")
        
        # 显示包含MsoNormalTable的内容样例
        print(f"\n--- 包含MsoNormalTable的内容样例 ---")
        html_col = 'html' if 'html' in df.columns else html_columns[0] if html_columns else None
        
        if html_col:
            mso_examples = 0
            for idx in range(len(df)):
                content = df[html_col].iloc[idx]
                if pd.notna(content) and isinstance(content, str):
                    has_mso = has_mso_normal_table(content)
                    if has_mso and mso_examples < num_samples:
                        mso_examples += 1
                        # 显示前500个字符
                        preview = content[:500] + "..." if len(content) > 500 else content
                        print(f"\n行{idx+1} (包含MsoNormalTable):")
                        print(f"内容: {preview}")
                        print("-" * 50)
                        
                        # 查找MsoNormalTable的具体位置
                        mso_matches = list(re.finditer(r'MsoNormalTable', content, re.IGNORECASE))
                        if mso_matches:
                            print(f"MsoNormalTable出现位置: {[m.start() for m in mso_matches]}")
                            print("-" * 50)
            
            if mso_examples == 0:
                print("未找到包含MsoNormalTable的内容")
                    
    except Exception as e:
        print(f"预览数据时出错: {e}")


def main():
    """主函数"""
    input_file = "/Users/<USER>/Downloads/国家能源局/政策通知/国家能源局-公告.xlsx"
    
    if os.path.exists(input_file):
        preview_mso_data(input_file)
    else:
        print(f"文件不存在: {input_file}")


if __name__ == "__main__":
    main()
